{"charts": [], "content": "[{\"id\":\"QKKWtrFvtI\",\"type\":\"custom_block\",\"data\":{\"custom_block_name\":\"Buildo Workspace Header\",\"col\":12}},{\"id\":\"XTSE9XS1bR\",\"type\":\"card\",\"data\":{\"card_name\":\"Order's\",\"col\":3}},{\"id\":\"PLy6sfBmo8\",\"type\":\"card\",\"data\":{\"card_name\":\"Customers\",\"col\":3}},{\"id\":\"ZJL0b87XeC\",\"type\":\"card\",\"data\":{\"card_name\":\"Contractors\",\"col\":3}},{\"id\":\"WkR8JiAlSd\",\"type\":\"card\",\"data\":{\"card_name\":\"Suppliers\",\"col\":3}},{\"id\":\"4WoaQDnZru\",\"type\":\"card\",\"data\":{\"card_name\":\"Projects\",\"col\":3}},{\"id\":\"SU4FB09m7F\",\"type\":\"card\",\"data\":{\"card_name\":\"HR\",\"col\":3}},{\"id\":\"4IRZNBtZRr\",\"type\":\"card\",\"data\":{\"card_name\":\"Material Registers\",\"col\":3}},{\"id\":\"nrlncbfh4U\",\"type\":\"card\",\"data\":{\"card_name\":\"Reports\",\"col\":3}}]", "creation": "2025-09-20 16:47:43.737272", "custom_blocks": [{"custom_block_name": "Buildo Workspace Header", "label": "Buildo Workspace Header"}], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "organization", "idx": 0, "indicator_color": "green", "is_hidden": 0, "label": "Buildo", "links": [{"hidden": 0, "is_query_report": 0, "label": "Customers", "link_count": 3, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Customers", "link_count": 0, "link_to": "Customers", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Customer Invoice", "link_count": 0, "link_to": "Client Invoice", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Payment Received", "link_count": 0, "link_to": "Payment Received", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Contractors", "link_count": 3, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Contractors", "link_count": 0, "link_to": "Contractors", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Contractor Schedules", "link_count": 0, "link_to": "C Schedule", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Payment to contractors", "link_count": 0, "link_to": "Contractor Payments", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Suppliers", "link_count": 3, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Suppliers", "link_count": 0, "link_to": "Suppliers", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Supplier Invoices", "link_count": 0, "link_to": "Supplier Invoices", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Payments to Suppliers", "link_count": 0, "link_to": "Payments Paid", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "HR", "link_count": 2, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Staff's", "link_count": 0, "link_to": "Staff", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Staff Salaries", "link_count": 0, "link_to": "Salary", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Projects", "link_count": 9, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Projects", "link_count": 0, "link_to": "Projects", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Project Schedules", "link_count": 0, "link_to": "P Schedules", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Project BOQ's", "link_count": 0, "link_to": "BOQ", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Petty Cash", "link_count": 0, "link_to": "Petty Cash", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Misc Expense", "link_count": 0, "link_to": "Misc Expense", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Labour Hajri's", "link_count": 0, "link_to": "<PERSON><PERSON><PERSON>", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Gate Pass", "link_count": 0, "link_to": "Gate Pass", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Checklist", "link_count": 0, "link_to": "Checklist", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "DPR", "link_count": 0, "link_to": "DPR", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Material Registers", "link_count": 12, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "RMC", "link_count": 0, "link_to": "RMC", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Steel", "link_count": 0, "link_to": "Steel Register", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Cement", "link_count": 0, "link_to": "Cement Register", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "AAC Block", "link_count": 0, "link_to": "AAC Block", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Bricks", "link_count": 0, "link_to": "Bricks", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Block Joint Mortar", "link_count": 0, "link_to": "Block Joint Mortar", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Ready Plast", "link_count": 0, "link_to": "Ready Plast", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Sand", "link_count": 0, "link_to": "Sand", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Crush Sand", "link_count": 0, "link_to": "Crush Sand", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "C<PERSON>", "link_count": 0, "link_to": "C<PERSON>", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Waterproofing", "link_count": 0, "link_to": "Waterproofing", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Misc Material", "link_count": 0, "link_to": "Misc Material", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Reports", "link_count": 15, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 1, "label": "Customer Ledger", "link_count": 0, "link_to": "Customer Ledger", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Customer Ledger Side by Side", "link_count": 0, "link_to": "Customer Ledger Side by Side", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Customer Invoice Details", "link_count": 0, "link_to": "Customer Invoice Details", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Contractor <PERSON><PERSON>", "link_count": 0, "link_to": "Contractor <PERSON><PERSON>", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Contractor Led<PERSON> Side by Side", "link_count": 0, "link_to": "Contractor Led<PERSON> Side by Side", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Supplier Ledger", "link_count": 0, "link_to": "Supplier Ledger", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Rmc Report", "link_count": 0, "link_to": "RMC Report", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Steel Report", "link_count": 0, "link_to": "Steel Report", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Cement Report", "link_count": 0, "link_to": "Cement Report", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "AAC Block & Bricks Report", "link_count": 0, "link_to": "AAC Brick Report", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "BJM & RP Report", "link_count": 0, "link_to": "BJM & RP Report", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Sand Report", "link_count": 0, "link_to": "Sand Report", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Crush Sand Report", "link_count": 0, "link_to": "Crush Sand Report", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "Cube Report", "link_count": 0, "link_to": "Cube Report", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 1, "label": "W P & Misc Material Report", "link_count": 0, "link_to": "WP & Misc Matrl Report", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Order's", "link_count": 2, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Work Order", "link_count": 0, "link_to": "Work Order", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Purchase Order", "link_count": 0, "link_to": "PO", "link_type": "DocType", "onboard": 0, "type": "Link"}], "modified": "2025-10-03 19:26:10.792676", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "Buildo", "number_cards": [], "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "roles": [], "sequence_id": 1.0, "shortcuts": [], "title": "Buildo"}