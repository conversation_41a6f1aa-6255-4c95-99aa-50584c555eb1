{"actions": [], "allow_rename": 1, "creation": "2025-10-03 18:38:47.486783", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item", "uom", "qty", "rate", "amount"], "fields": [{"fieldname": "item", "fieldtype": "Data", "in_list_view": 1, "label": "<PERSON><PERSON>"}, {"fieldname": "uom", "fieldtype": "Data", "in_list_view": 1, "label": "UOM"}, {"fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty", "precision": "2"}, {"fieldname": "rate", "fieldtype": "Float", "in_list_view": 1, "label": "Rate", "precision": "2"}, {"fieldname": "amount", "fieldtype": "Float", "in_list_view": 1, "label": "Amount", "precision": "2", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-10-03 18:42:42.448787", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "PO table", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}