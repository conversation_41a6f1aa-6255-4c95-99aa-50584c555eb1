// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on('Salary', {
    refresh: function(frm) {
        frm.add_custom_button(__('Fetch Staff'), function() {
            frm.clear_table('table_junc');
            frappe.call({
                method: 'buildofrappe.buildo_frappe.doctype.salary.salary.fetch_active_staff',
                callback: function(r) {
                    if (r.message) {
                        r.message.forEach(function(staff) {
                            let row = frm.add_child('table_junc');
                            row.staff_name = staff.staff_name;
                            row.salary = staff.salary;
                            // Initialize allowance, deductions, and net_salary for new rows
                            row.allowance = 0;
                            row.deductions = 0;
                            row.net_salary = staff.salary; // Initial net_salary is just salary
                        });
                        frm.refresh_field('table_junc');
                        calculate_totals(frm); // Calculate totals after fetching staff
                    }
                }
            });
        });
    },
    table_junc_add: function(frm) {
        calculate_totals(frm);
    },
    table_junc_remove: function(frm) {
        calculate_totals(frm);
    }
});

frappe.ui.form.on('salary table', {
    salary: function(frm, cdt, cdn) {
        calculate_net_salary(frm, cdt, cdn);
        calculate_totals(frm);
    },
    allowance: function(frm, cdt, cdn) {
        calculate_net_salary(frm, cdt, cdn);
        calculate_totals(frm);
    },
    deductions: function(frm, cdt, cdn) {
        calculate_net_salary(frm, cdt, cdn);
        calculate_totals(frm);
    },
    net_salary: function(frm, cdt, cdn) {
        calculate_totals(frm); // Recalculate totals if net_salary changes (e.g., manually edited)
    },
    table_junc_on_form_rendered: function(frm) {
        calculate_totals(frm);
    }
});

function calculate_net_salary(frm, cdt, cdn) {
    let row = frappe.get_doc(cdt, cdn);
    let salary = flt(row.salary);
    let allowance = flt(row.allowance);
    let deductions = flt(row.deductions);
    let net_salary = salary + allowance - deductions;
    frappe.model.set_value(cdt, cdn, 'net_salary', net_salary);
}

function calculate_totals(frm) {
    let total_salary = 0;
    let total_allowance = 0;
    let total_deduction = 0;
    let total_net_payable = 0;

    frm.doc.table_junc.forEach(function(row) {
        total_salary += flt(row.salary);
        total_allowance += flt(row.allowance);
        total_deduction += flt(row.deductions);
        total_net_payable += flt(row.net_salary);
    });

    frm.set_value('total_salary', total_salary);
    frm.set_value('total_allowance', total_allowance);
    frm.set_value('total_deduction', total_deduction);
    frm.set_value('total_net_payable', total_net_payable);
}

