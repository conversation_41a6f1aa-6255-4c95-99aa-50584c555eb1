// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("PO", {
	refresh(frm) {
		// Calculate totals on form load
		calculate_totals(frm);
	},

	onload(frm) {
		// Calculate totals when form loads
		calculate_totals(frm);
	}
});

// Handle changes in the PO table
frappe.ui.form.on("PO table", {
	qty(frm, cdt, cdn) {
		calculate_row_amount(frm, cdt, cdn);
	},

	rate(frm, cdt, cdn) {
		calculate_row_amount(frm, cdt, cdn);
	},

	table_bjef_add(frm, cdt, cdn) {
		// When a new row is added
		calculate_totals(frm);
	},

	table_bjef_remove(frm, cdt, cdn) {
		// When a row is removed
		calculate_totals(frm);
	}
});

// Calculate amount for a single row (rate * qty)
function calculate_row_amount(frm, cdt, cdn) {
	let row = locals[cdt][cdn];

	// Calculate amount = rate * qty
	let qty = flt(row.qty) || 0;
	let rate = flt(row.rate) || 0;
	let amount = qty * rate;

	// Set the amount in the row
	frappe.model.set_value(cdt, cdn, 'amount', amount);

	// Recalculate totals
	calculate_totals(frm);
}

// Calculate total amount and amount in words
function calculate_totals(frm) {
	let total = 0;

	// Sum up all amounts from the table
	if (frm.doc.table_bjef) {
		frm.doc.table_bjef.forEach(function(row) {
			total += flt(row.amount) || 0;
		});
	}

	// Set total amount
	frm.set_value('total_amount', total);

	// Convert to words
	if (total > 0) {
		let amount_in_words = in_words(total);
		frm.set_value('amount_in_words', amount_in_words);
	} else {
		frm.set_value('amount_in_words', '');
	}
}

// Function to convert number to words (Indian currency format)
function in_words(num) {
	var a = ['','One','Two','Three','Four','Five','Six','Seven','Eight','Nine','Ten','Eleven','Twelve','Thirteen','Fourteen','Fifteen','Sixteen','Seventeen','Eighteen','Nineteen'];
	var b = ['', '', 'Twenty','Thirty','Forty','Fifty','Sixty','Seventy','Eighty','Ninety'];

	function convert_chunk(n) {
		var s = '';
		if (n < 20) {
			s = a[n];
		} else {
			s = b[Math.floor(n / 10)] + ' ' + a[n % 10];
		}
		return s.trim();
	}

	// Split into integer and decimal parts
	let num_str = num.toFixed(2); // Ensure 2 decimal places
	let [integer_part, decimal_part] = num_str.split('.');
	integer_part = parseInt(integer_part) || 0;
	decimal_part = parseInt(decimal_part) || 0;

	let words = '';

	if (integer_part === 0 && decimal_part === 0) {
		return 'Rupees Zero Only';
	}

	if (integer_part > 0) {
		// Pad to 9 digits for crore, lakh, thousand, hundred, tens
		let n = ('000000000' + integer_part).slice(-9).match(/^(\d{2})(\d{2})(\d{2})(\d{1})(\d{2})$/);
		if (!n) return 'Error'; // Should not happen with valid numbers

		let str = '';
		str += (n[1] != 0) ? convert_chunk(Number(n[1])) + ' Crore ' : '';
		str += (n[2] != 0) ? convert_chunk(Number(n[2])) + ' Lakh ' : '';
		str += (n[3] != 0) ? convert_chunk(Number(n[3])) + ' Thousand ' : '';
		str += (n[4] != 0) ? convert_chunk(Number(n[4])) + ' Hundred ' : '';
		str += (n[5] != 0) ? convert_chunk(Number(n[5])) : '';
		words += 'Rupees ' + str.trim();
	}

	if (decimal_part > 0) {
		if (integer_part > 0) {
			words += ' and ';
		} else {
			words += 'Rupees ';
		}
		words += convert_chunk(decimal_part) + ' Paise';
	}

	words += ' Only';
	return words;
}

// Helper function to safely parse float values
function flt(value, default_value = 0) {
	let parsed = parseFloat(value);
	return isNaN(parsed) ? default_value : parsed;
}
