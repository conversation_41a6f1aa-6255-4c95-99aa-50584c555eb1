# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import flt


class PO(Document):
	def validate(self):
		"""Validate and calculate totals before saving"""
		self.calculate_totals()

	def calculate_totals(self):
		"""Calculate total amount and amount in words"""
		total = 0

		# Calculate amount for each row and sum up
		for row in self.get("table_bjef", []):
			# Calculate row amount = qty * rate
			row.amount = flt(row.qty) * flt(row.rate)
			total += row.amount

		# Set total amount
		self.total_amount = total

		# Convert to words
		if total > 0:
			self.amount_in_words = self.number_to_words(total)
		else:
			self.amount_in_words = ""

	def number_to_words(self, num):
		"""Convert number to words in Indian currency format"""
		ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine',
				'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen',
				'Seventeen', 'Eighteen', 'Nineteen']
		tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety']

		def convert_chunk(n):
			"""Convert a number less than 100 to words"""
			if n < 20:
				return ones[n]
			else:
				return (tens[n // 10] + ' ' + ones[n % 10]).strip()

		# Split into integer and decimal parts
		num_str = "{:.2f}".format(num)
		integer_part, decimal_part = num_str.split('.')
		integer_part = int(integer_part)
		decimal_part = int(decimal_part)

		if integer_part == 0 and decimal_part == 0:
			return 'Rupees Zero Only'

		words = ''

		if integer_part > 0:
			# Process crores (10,000,000)
			crores = integer_part // 10000000
			if crores > 0:
				words += convert_chunk(crores) + ' Crore '
				integer_part %= 10000000

			# Process lakhs (100,000)
			lakhs = integer_part // 100000
			if lakhs > 0:
				words += convert_chunk(lakhs) + ' Lakh '
				integer_part %= 100000

			# Process thousands (1,000)
			thousands = integer_part // 1000
			if thousands > 0:
				words += convert_chunk(thousands) + ' Thousand '
				integer_part %= 1000

			# Process hundreds (100)
			hundreds = integer_part // 100
			if hundreds > 0:
				words += convert_chunk(hundreds) + ' Hundred '
				integer_part %= 100

			# Process remaining (less than 100)
			if integer_part > 0:
				words += convert_chunk(integer_part)

			words = 'Rupees ' + words.strip()

		# Add paise if present
		if decimal_part > 0:
			if integer_part > 0:
				words += ' and '
			else:
				words += 'Rupees '
			words += convert_chunk(decimal_part) + ' Paise'

		words += ' Only'
		return words
