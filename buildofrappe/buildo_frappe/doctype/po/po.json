{"actions": [], "allow_rename": 1, "autoname": "format:PO-{project}-{DD}-{MM}-{YYYY}-{######}", "creation": "2025-10-03 18:40:29.590444", "doctype": "DocType", "engine": "InnoDB", "field_order": ["supplier", "supplier_address", "supplier_gstin_no", "column_break_vvel", "date", "company", "company_gstin_no", "project", "section_break_katu", "subject", "section_break_poyv", "table_bjef", "section_break_mqow", "column_break_vtgp", "column_break_mfcu", "total_amount", "section_break_utvt", "amount_in_words", "section_break_bhij", "terms_conditions"], "fields": [{"fieldname": "supplier", "fieldtype": "Link", "label": "Supplier", "options": "Suppliers"}, {"fieldname": "supplier_address", "fieldtype": "Small Text", "label": "Supplier Address"}, {"fieldname": "supplier_gstin_no", "fieldtype": "Data", "label": "Supplier GSTIN NO", "link_filters": "[[\"Suppliers\",\"gst_no\",\"=\",\"suppliers\"]]"}, {"fieldname": "column_break_vvel", "fieldtype": "Column Break"}, {"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"fieldname": "company", "fieldtype": "Data", "label": "Company"}, {"fieldname": "company_gstin_no", "fieldtype": "Data", "label": "Company GSTIN NO"}, {"fieldname": "section_break_katu", "fieldtype": "Section Break"}, {"fieldname": "subject", "fieldtype": "Data", "label": "Subject"}, {"fieldname": "section_break_poyv", "fieldtype": "Section Break"}, {"fieldname": "table_bjef", "fieldtype": "Table", "options": "PO table"}, {"fieldname": "section_break_mqow", "fieldtype": "Section Break"}, {"fieldname": "column_break_vtgp", "fieldtype": "Column Break"}, {"fieldname": "column_break_mfcu", "fieldtype": "Column Break"}, {"fieldname": "total_amount", "fieldtype": "Float", "label": "Total Amount", "read_only": 1}, {"fieldname": "section_break_utvt", "fieldtype": "Section Break"}, {"fieldname": "amount_in_words", "fieldtype": "Data", "label": "Amount In Words", "read_only": 1}, {"fieldname": "section_break_bhij", "fieldtype": "Section Break"}, {"fieldname": "terms_conditions", "fieldtype": "Text Editor", "label": "Terms & Conditions"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Projects"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-10-03 18:56:53.259829", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "PO", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}