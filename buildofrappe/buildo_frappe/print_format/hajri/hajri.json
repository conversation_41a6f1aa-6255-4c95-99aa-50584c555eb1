{"absolute_value": 0, "align_labels_right": 0, "creation": "2025-09-27 16:42:10.489478", "css": "/* Hajri Custom Print Format Styles */\n\n/* Reset and Base Styles */\n* {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n}\n\nbody {\n    font-family: Arial, sans-serif;\n    font-size: 12px;\n    line-height: 1.4;\n    color: #000;\n    background: #fff;\n    padding: 2px;\n}\n\n/* Header Styles */\n.header {\n    text-align: center;\n    font-weight: bold;\n    font-size: 16px;\n    margin-bottom: 20px;\n    text-decoration: underline;\n    letter-spacing: 1px;\n}\n\n/* Information Section */\n.info-section {\n    margin-bottom: 20px;\n}\n\n.info-row {\n    display: flex;\n    margin-bottom: 5px;\n    align-items: center;\n}\n\n.info-label {\n    font-weight: bold;\n    width: 120px;\n    flex-shrink: 0;\n}\n\n.date-info {\n    margin-left: auto;\n    font-weight: bold;\n    text-align: left;\n}\n\n/* Section Titles */\n.section-title {\n    font-weight: bold;\n    text-align: center;\n    margin: 20px 0 10px 0;\n    text-decoration: underline;\n    font-size: 14px;\n}\n\n.subsection-title {\n    font-weight: bold;\n    text-decoration: underline;\n    text-align: center;\n    margin-bottom: 10px;\n    font-size: 12px;\n}\n\n/* Table Styles */\ntable {\n    width: 100%;\n    border-collapse: collapse;\n    margin-bottom: 20px;\n    font-size: 11px;\n}\n\nth, td {\n    border: 1px solid #000;\n    padding: 4px 6px;\n    text-align: center;\n    vertical-align: middle;\n}\n\nth {\n    background-color: #f0f0f0;\n    font-weight: bold;\n    font-size: 10px;\n}\n\n.text-left {\n    text-align: left !important;\n}\n\n/* Manpower Table */\n.manpower-table th:first-child,\n.manpower-table td:first-child {\n    width: 5%;\n}\n\n.manpower-table th:nth-child(2),\n.manpower-table td:nth-child(2) {\n    width: 25%;\n}\n\n.manpower-table th:nth-child(n+3),\n.manpower-table td:nth-child(n+3) {\n    width: 8.75%;\n}\n\n.total-row {\n    font-weight: bold;\n    background-color: #f8f8f8;\n}\n\n/* Work Status */\n.work-status-container {\n    display: flex;\n    gap: 20px;\n    margin-bottom: 20px;\n}\n\n.work-status-left,\n.work-status-right {\n    flex: 1;\n}\n\n.work-status-table {\n    width: 100%;\n}\n\n.work-status-table th:first-child,\n.work-status-table td:first-child {\n    width: 10%;\n}\n\n.work-status-table th:nth-child(2),\n.work-status-table td:nth-child(2) {\n    width: 90%;\n}\n\n/* Hold Ups and Request Sections */\n.hold-ups-section,\n.request-info-section {\n    margin: 20px 0;\n}\n\n.hold-ups-section h4,\n.request-info-section h4 {\n    font-weight: bold;\n    text-decoration: underline;\n    text-align: center;\n    margin-bottom: 10px;\n    font-size: 12px;\n}\n\n.content-box {\n    min-height: 30px;\n    padding: 5px;\n    border: 1px solid #ccc;\n    background-color: #fafafa;\n}\n\n/* Equipment and Material Container */\n.equipment-material-container {\n    display: flex;\n    gap: 20px;\n    margin-bottom: 20px;\n}\n\n.equipment-section,\n.material-section {\n    flex: 1;\n}\n\n/* Equipment Table */\n.equipment-table th:first-child,\n.equipment-table td:first-child {\n    width: 10%;\n}\n\n.equipment-table th:nth-child(2),\n.equipment-table td:nth-child(2) {\n    width: 70%;\n}\n\n.equipment-table th:nth-child(3),\n.equipment-table td:nth-child(3) {\n    width: 20%;\n}\n\n/* Material Table */\n.material-table th:first-child,\n.material-table td:first-child {\n    width: 8%;\n}\n\n.material-table th:nth-child(2),\n.material-table td:nth-child(2) {\n    width: 30%;\n}\n\n.material-table th:nth-child(3),\n.material-table td:nth-child(3) {\n    width: 12%;\n}\n\n.material-table th:nth-child(4),\n.material-table td:nth-child(4) {\n    width: 25%;\n}\n\n.material-table th:nth-child(5),\n.material-table td:nth-child(5) {\n    width: 25%;\n}\n\n/* Material Status Section */\n.material-status-section {\n    margin-top: 20px;\n}\n\n.material-status-section h4 {\n    font-weight: bold;\n    text-decoration: underline;\n    margin-bottom: 10px;\n    font-size: 12px;\n}\n\n.material-status-content {\n    padding: 10px;\n    border: 1px solid #ccc;\n    background-color: #fafafa;\n}\n\n.material-category {\n    margin-bottom: 10px;\n}\n\n.material-category strong {\n    display: block;\n    margin-bottom: 5px;\n    text-decoration: underline;\n}\n\n.material-items {\n    margin-left: 10px;\n    line-height: 1.6;\n}\n\n/* Print Specific Styles */\n@media print {\n    body {\n        padding: 2px;\n        font-size: 11px;\n    }\n    \n    .header {\n        font-size: 14px;\n    }\n    \n    table {\n        font-size: 10px;\n    }\n    \n    th, td {\n        padding: 3px 4px;\n    }\n    \n    .work-status-container,\n    .equipment-material-container {\n        gap: 15px;\n    }\n}\n\n/* Responsive Design for smaller screens */\n@media screen and (max-width: 768px) {\n    .work-status-container,\n    .equipment-material-container {\n        flex-direction: column;\n        gap: 10px;\n    }\n    \n    .info-row {\n        flex-direction: column;\n        align-items: flex-start;\n    }\n    \n    .date-info {\n        margin-left: 0;\n        margin-top: 5px;\n    }\n}\n\n/* Utility Classes */\n.no-border {\n    border: none !important;\n}\n\n.bold {\n    font-weight: bold;\n}\n\n.center {\n    text-align: center;\n}\n\n.underline {\n    text-decoration: underline;\n}\n\n.small-text {\n    font-size: 10px;\n}\n\n.large-text {\n    font-size: 14px;\n}", "custom_format": 1, "default_print_language": "en", "disabled": 0, "doc_type": "<PERSON><PERSON><PERSON>", "docstatus": 0, "doctype": "Print Format", "font_size": 14, "html": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"UTF-8\">\n    <title>Hajri</title>\n    <link rel=\"stylesheet\" href=\"hajri_styles.css\">\n</head>\n<body>\n    <!-- Header -->\n    <div class=\"header\">\n        Labour Hajri / Payment Details\n    </div>\n    \n    <!-- Basic Information -->\n    <div class=\"info-section\">\n        <div class=\"info-row\">\n            <span class=\"info-label\">Project : {{ doc.project }}</span>\n            <span class=\"date-info\">For Month : {{doc.for_month_year}} </span>\n            <span class=\"date-info\">Department : {{doc.department}} </span>\n        </div>\n    </div>\n    \n    <!-- Manpower Strength Report -->\n    <div class=\"section-title\">Details</div>\n    <table class=\"manpower-table\">\n        <thead>\n            <tr>\n                <th>No</th>\n                <th>Labour Name</th>\n                <th>Rate</th>\n                <th>Total Hajri</th>\n                <th>Amount</th>\n                <th>Advance</th>\n                <th>Net Pay</th>\n                <th>Remarks</th>\n            </tr>\n        </thead>\n        <tbody>\n            {% for row in doc.table_xwtx %}\n            <tr>\n                <td>{{ loop.index }}</td>\n                <td class=\"text-left\">{{ row.labour_name or '' }}</td>\n                <td>{{ row.rate or 0 }}</td>\n                <td>{{ row.total_hajri or 0 }}</td>\n                <td>{{ row.amount or 0 }}</td>\n                <td>{{ row.advances or 0 }}</td>\n                <td>{{ row.net_payable or 0 }}</td>\n                <td>{{ row.remarks or 0 }}</td>\n            </tr>\n            {% endfor %}\n        </tbody>\n    </table>\n        <!-- Total(s) -->\n    <div class=\"info-section\">\n        <div class=\"info-row\">\n            <span class=\"date-info\">Total Amount : {{ doc.total_amount }}</span>\n            <span class=\"date-info\">Total Advance : {{doc.total_advance}} </span>\n            <span class=\"date-info\">Total Net Pay : {{doc.total_net_payable}} </span>\n        </div>\n    </div>\n        <div class=\"request-info-section\">\n        <h4>Remarks</h4>\n        <div class=\"content-box\">{{ doc.remarks or 'None' }}</div>\n    </div>\n\n\n</body>\n</html>", "idx": 0, "line_breaks": 0, "margin_bottom": 5.0, "margin_left": 5.0, "margin_right": 5.0, "margin_top": 5.0, "modified": "2025-09-29 11:18:05.836132", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "<PERSON><PERSON><PERSON>", "owner": "Administrator", "page_number": "Bottom Right", "pdf_generator": "chrome", "print_designer": 0, "print_designer_template_app": "print_designer", "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_for": "DocType", "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}