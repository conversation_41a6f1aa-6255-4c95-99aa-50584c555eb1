{"absolute_value": 0, "align_labels_right": 0, "creation": "2025-09-25 19:10:24.555564", "custom_format": 0, "default_print_language": "en", "disabled": 0, "doc_type": "DPR", "docstatus": 0, "doctype": "Print Format", "font_size": 14, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<h4 style=\\\"text-align: center;\\\">Yogi Buildcon Daily Progress Report</h4>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"client\", \"print_hide\": 0, \"label\": \"Client \"}, {\"fieldname\": \"project\", \"print_hide\": 0, \"label\": \"Project\"}, {\"fieldname\": \"address\", \"print_hide\": 0, \"label\": \"Address\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"date\", \"print_hide\": 0, \"label\": \"Date\"}, {\"fieldname\": \"total_days\", \"print_hide\": 0, \"label\": \"Total Days\"}, {\"fieldname\": \"days_elapsed\", \"print_hide\": 0, \"label\": \"Days Elapsed\"}, {\"fieldname\": \"balance\", \"print_hide\": 0, \"label\": \"Balance\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Manpower Report\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"manpower_table\", \"print_hide\": 0, \"visible_columns\": [{\"fieldname\": \"description\", \"print_width\": \"2\", \"print_hide\": 0}, {\"fieldname\": \"prev\", \"print_width\": \"1\", \"print_hide\": 0}, {\"fieldname\": \"today\", \"print_width\": \"1\", \"print_hide\": 0}, {\"fieldname\": \"specialized\", \"print_width\": \"1\", \"print_hide\": 0}, {\"fieldname\": \"carp\", \"print_width\": \"1\", \"print_hide\": 0}, {\"fieldname\": \"fitt\", \"print_width\": \"1\", \"print_hide\": 0}, {\"fieldname\": \"b_mas\", \"print_width\": \"1\", \"print_hide\": 0}, {\"fieldname\": \"pmas\", \"print_width\": \"1\", \"print_hide\": 0}, {\"fieldname\": \"mc\", \"print_width\": \"1\", \"print_hide\": 0}]}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"total_prev\", \"print_hide\": 0, \"label\": \"Total Prev\"}, {\"fieldname\": \"total_today\", \"print_hide\": 0, \"label\": \"Total Today\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"total_spec\", \"print_hide\": 0, \"label\": \"Total Spec\"}, {\"fieldname\": \"total_carp\", \"print_hide\": 0, \"label\": \"Total Carp\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"total_fitt\", \"print_hide\": 0, \"label\": \"Total Fitt\"}, {\"fieldname\": \"total_p_mas\", \"print_hide\": 0, \"label\": \"Total P Mas\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"total_b_mas\", \"print_hide\": 0, \"label\": \"Total B Mas\"}, {\"fieldname\": \"total_mc\", \"print_hide\": 0, \"label\": \"Total MC\"}, {\"fieldtype\": \"Section Break\", \"label\": \"Work Status\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"wdone_today_table\", \"print_hide\": 0, \"visible_columns\": [{\"fieldname\": \"work_done_today\", \"print_width\": \"\", \"print_hide\": 0}]}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"wplanned_tomm_table\", \"print_hide\": 0, \"visible_columns\": [{\"fieldname\": \"work_planned_tommorow\", \"print_width\": \"\", \"print_hide\": 0}]}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"hold_ups_stoppages_of_works_with_reasons\", \"print_hide\": 0, \"label\": \"Hold Ups /Stoppages of works with reasons\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"request_for_information\", \"print_hide\": 0, \"label\": \" Request for information \"}, {\"fieldtype\": \"Section Break\", \"label\": \"Equipment / Machinery Deployed & Material Receipt\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"mach_deployed_table\", \"print_hide\": 0, \"visible_columns\": [{\"fieldname\": \"machinery_deployed\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"nos\", \"print_width\": \"\", \"print_hide\": 0}]}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"mat_recp_table\", \"print_hide\": 0, \"visible_columns\": [{\"fieldname\": \"material\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"qty\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"desc\", \"print_width\": \"\", \"print_hide\": 0}, {\"fieldname\": \"remark\", \"print_width\": \"\", \"print_hide\": 0}]}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"status_of_material\", \"print_hide\": 0, \"label\": \"Status of Material\"}]", "idx": 0, "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2025-09-25 19:43:10.151271", "modified_by": "Administrator", "module": "Buildo Frappe", "name": "DPR Email", "owner": "Administrator", "page_number": "Bottom Right", "pdf_generator": "chrome", "print_designer": 0, "print_designer_template_app": "print_designer", "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_for": "DocType", "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}