// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["WP & Misc Matrl Report"] = {
	"filters": [
		{
			"fieldname": "material_type",
			"label": __("Material Type"),
			"fieldtype": "Select",
			"options": ["", "Waterproofing", "Misc Material"],
			"reqd": 1,
			"default": ""
		},
		{
			"fieldname": "project",
			"label": __("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"reqd": 0
		},
		{
			"fieldname": "supplier",
			"label": __("Supplier"),
			"fieldtype": "Link",
			"options": "Suppliers",
			"reqd": 0
		},
		{
			"fieldname": "material",
			"label": __("Material"),
			"fieldtype": "Data",
			"reqd": 0
		},
		{
			"fieldname": "from_date",
			"label": __("From Date"),
			"fieldtype": "Date",
			"reqd": 0
		},
		{
			"fieldname": "to_date",
			"label": __("To Date"),
			"fieldtype": "Date",
			"reqd": 0
		}
	]
};
