# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _


def execute(filters=None):
	if not filters:
		filters = {}

	# Validate that material_type is selected
	if not filters.get("material_type"):
		frappe.throw(_("Please select Material Type"))

	columns = get_columns()
	data = get_data(filters)

	return columns, data


def get_columns():
	"""Define columns for the report"""
	return [
		{
			"fieldname": "name",
			"label": _("ID"),
			"fieldtype": "Link",
			"options": "Waterproofing",  # Will be dynamic based on material type
			"width": 180
		},
		{
			"fieldname": "date",
			"label": _("Date"),
			"fieldtype": "Date",
			"width": 100
		},
		{
			"fieldname": "project",
			"label": _("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"width": 150
		},
		{
			"fieldname": "supplier",
			"label": _("Supplier"),
			"fieldtype": "Link",
			"options": "Suppliers",
			"width": 150
		},
		{
			"fieldname": "material",
			"label": _("Material"),
			"fieldtype": "Data",
			"width": 150
		},
		{
			"fieldname": "challan_no",
			"label": _("Challan No"),
			"fieldtype": "Data",
			"width": 120
		},
		{
			"fieldname": "vehicle_no",
			"label": _("Vehicle No"),
			"fieldtype": "Data",
			"width": 120
		},
		{
			"fieldname": "qty",
			"label": _("Qty"),
			"fieldtype": "Data",
			"width": 100
		},
		{
			"fieldname": "remarks",
			"label": _("Remarks"),
			"fieldtype": "Small Text",
			"width": 200
		}
	]


def get_data(filters):
	"""Fetch data based on selected material type and filters"""

	# Determine which doctype to query based on material_type filter
	material_type = filters.get("material_type")

	if material_type == "Waterproofing":
		doctype = "Waterproofing"
	elif material_type == "Misc Material":
		doctype = "Misc Material"
	else:
		return []

	# Build conditions for the query
	conditions = []
	values = {}

	# Add project filter if provided
	if filters.get("project"):
		conditions.append("project = %(project)s")
		values["project"] = filters.get("project")

	# Add supplier filter if provided
	if filters.get("supplier"):
		conditions.append("supplier = %(supplier)s")
		values["supplier"] = filters.get("supplier")

	# Add material filter if provided
	if filters.get("material"):
		conditions.append("material LIKE %(material)s")
		values["material"] = f"%{filters.get('material')}%"

	# Add date range filters
	if filters.get("from_date"):
		conditions.append("date >= %(from_date)s")
		values["from_date"] = filters.get("from_date")

	if filters.get("to_date"):
		conditions.append("date <= %(to_date)s")
		values["to_date"] = filters.get("to_date")

	# Build WHERE clause
	where_clause = ""
	if conditions:
		where_clause = "WHERE " + " AND ".join(conditions)

	# Execute query
	query = f"""
		SELECT
			name,
			date,
			project,
			supplier,
			material,
			challan_no,
			vehicle_no,
			qty,
			remarks
		FROM
			`tab{doctype}`
		{where_clause}
		ORDER BY
			date DESC, name DESC
	"""

	data = frappe.db.sql(query, values, as_dict=1)

	return data
