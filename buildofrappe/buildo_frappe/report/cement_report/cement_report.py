# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _


def execute(filters=None):
	columns = get_columns()
	data = get_data(filters)
	return columns, data


def get_columns():
	"""Define the columns for the report"""
	return [
		{
			"fieldname": "date",
			"label": _("Date"),
			"fieldtype": "Date",
			"width": 100
		},
		{
			"fieldname": "supplier",
			"label": _("Supplier"),
			"fieldtype": "Link",
			"options": "Suppliers",
			"width": 150
		},
		{
			"fieldname": "project",
			"label": _("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"width": 150
		},
		{
			"fieldname": "grade",
			"label": _("Grade"),
			"fieldtype": "Data",
			"width": 100
		},
		{
			"fieldname": "vehicle_no",
			"label": _("Vehicle No"),
			"fieldtype": "Data",
			"width": 120
		},
		{
			"fieldname": "challan_no",
			"label": _("Challan No"),
			"fieldtype": "Data",
			"width": 120
		},
		{
			"fieldname": "total_bags",
			"label": _("Total Bags"),
			"fieldtype": "Float",
			"width": 100
		}
	]


def get_data(filters):
	"""Get data based on filters"""
	conditions = get_conditions(filters)

	data = frappe.db.sql("""
		SELECT
			date,
			supplier,
			project,
			grade,
			vehicle_no,
			challan_no,
			total_bags
		FROM `tabCement Register`
		WHERE docstatus != 2 {conditions}
		ORDER BY date DESC, creation DESC
	""".format(conditions=conditions), filters, as_dict=1)

	return data


def get_conditions(filters):
	"""Build WHERE conditions based on filters"""
	conditions = []

	if filters.get("supplier"):
		conditions.append("AND supplier = %(supplier)s")

	if filters.get("project"):
		conditions.append("AND project = %(project)s")

	if filters.get("grade"):
		conditions.append("AND grade = %(grade)s")

	if filters.get("from_date"):
		conditions.append("AND date >= %(from_date)s")

	if filters.get("to_date"):
		conditions.append("AND date <= %(to_date)s")

	return " ".join(conditions)
