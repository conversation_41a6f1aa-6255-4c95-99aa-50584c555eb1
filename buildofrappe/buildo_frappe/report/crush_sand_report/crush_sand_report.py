# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _


def execute(filters=None):
	if not filters:
		filters = {}

	# Validate date range
	if filters.get("from_date") and filters.get("to_date"):
		from frappe.utils import getdate
		if getdate(filters.get("from_date")) > getdate(filters.get("to_date")):
			frappe.throw(_("From Date cannot be greater than To Date"))

	columns = get_columns()
	data = get_data(filters)

	return columns, data


def get_columns():
	"""Define columns for the report"""
	columns = [
		{
			"fieldname": "name",
			"label": _("ID"),
			"fieldtype": "Link",
			"options": "Crush Sand",
			"width": 180
		},
		{
			"fieldname": "date",
			"label": _("Date"),
			"fieldtype": "Date",
			"width": 100
		},
		{
			"fieldname": "project",
			"label": _("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"width": 150
		},
		{
			"fieldname": "supplier",
			"label": _("Supplier"),
			"fieldtype": "Link",
			"options": "Suppliers",
			"width": 150
		},
		{
			"fieldname": "challan_no",
			"label": _("Challan No"),
			"fieldtype": "Data",
			"width": 120
		},
		{
			"fieldname": "vehicle_no",
			"label": _("Vehicle No"),
			"fieldtype": "Data",
			"width": 120
		},
		{
			"fieldname": "qty",
			"label": _("Quantity"),
			"fieldtype": "Float",
			"width": 100
		},
		{
			"fieldname": "remarks",
			"label": _("Remarks"),
			"fieldtype": "Small Text",
			"width": 200
		}
	]
	return columns


def get_conditions(filters):
	"""Build WHERE conditions based on filters"""
	conditions = []
	values = {}

	# Project filter
	if filters.get("project"):
		conditions.append("project = %(project)s")
		values["project"] = filters.get("project")

	# Supplier filter
	if filters.get("supplier"):
		conditions.append("supplier = %(supplier)s")
		values["supplier"] = filters.get("supplier")

	# Date range filters
	if filters.get("from_date"):
		conditions.append("date >= %(from_date)s")
		values["from_date"] = filters.get("from_date")

	if filters.get("to_date"):
		conditions.append("date <= %(to_date)s")
		values["to_date"] = filters.get("to_date")

	return conditions, values


def get_data(filters):
	"""Get the data for the report based on filters"""
	conditions, values = get_conditions(filters)

	# Build the WHERE clause
	where_clause = ""
	if conditions:
		where_clause = "WHERE " + " AND ".join(conditions)

	# Execute the query
	query = f"""
		SELECT
			name,
			date,
			project,
			supplier,
			challan_no,
			vehicle_no,
			qty,
			remarks
		FROM `tabCrush Sand`
		{where_clause}
		ORDER BY date DESC, name DESC
	"""

	try:
		data = frappe.db.sql(query, values, as_dict=True)
		return data
	except Exception as e:
		frappe.log_error(f"Error in Crush Sand Report query: {str(e)}")
		frappe.throw(_("Error fetching data. Please check the filters and try again."))
