# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _


def execute(filters=None):
	columns = get_columns()
	data = get_data(filters)
	return columns, data


def get_columns():
	"""Define the columns for the report"""
	return [
		{
			"fieldname": "name",
			"label": _("Cube ID"),
			"fieldtype": "Link",
			"options": "Cube",
			"width": 200
		},
		{
			"fieldname": "date_of_casting",
			"label": _("Date of Casting"),
			"fieldtype": "Date",
			"width": 120
		},
		{
			"fieldname": "project",
			"label": _("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"width": 150
		},
		{
			"fieldname": "grade",
			"label": _("Grade"),
			"fieldtype": "Data",
			"width": 100
		},
		{
			"fieldname": "member_id",
			"label": _("Member ID"),
			"fieldtype": "Data",
			"width": 120
		},
		{
			"fieldname": "age",
			"label": _("Age"),
			"fieldtype": "Data",
			"width": 80
		},
		{
			"fieldname": "t_date",
			"label": _("T Date"),
			"fieldtype": "Date",
			"width": 120
		},
		{
			"fieldname": "weight",
			"label": _("Weight"),
			"fieldtype": "Float",
			"width": 100
		},
		{
			"fieldname": "load_kn",
			"label": _("Load K/N"),
			"fieldtype": "Float",
			"width": 100
		},
		{
			"fieldname": "strength_nmm2",
			"label": _("Strength N/mm2"),
			"fieldtype": "Float",
			"width": 120
		},
		{
			"fieldname": "avg",
			"label": _("AVG"),
			"fieldtype": "Float",
			"width": 100
		},
		{
			"fieldname": "remarks",
			"label": _("Remarks"),
			"fieldtype": "Small Text",
			"width": 200
		}
	]


def get_data(filters):
	"""Fetch data based on filters"""
	conditions = []
	values = {}

	# Build the WHERE clause based on filters
	if filters.get("project"):
		conditions.append("c.project = %(project)s")
		values["project"] = filters.get("project")

	if filters.get("grade"):
		conditions.append("c.grade = %(grade)s")
		values["grade"] = filters.get("grade")

	if filters.get("from_date"):
		conditions.append("c.date_of_casting >= %(from_date)s")
		values["from_date"] = filters.get("from_date")

	if filters.get("to_date"):
		conditions.append("c.date_of_casting <= %(to_date)s")
		values["to_date"] = filters.get("to_date")

	if filters.get("age"):
		conditions.append("ct.age = %(age)s")
		values["age"] = filters.get("age")

	where_clause = ""
	if conditions:
		where_clause = "WHERE " + " AND ".join(conditions)

	# Query to fetch data from Cube and Cube Table
	query = f"""
		SELECT
			c.name,
			c.date_of_casting,
			c.project,
			c.grade,
			c.member_id,
			ct.age,
			ct.t_date,
			ct.weight,
			ct.load_kn,
			ct.strength_nmm2,
			ct.avg,
			c.remarks
		FROM
			`tabCube` c
		LEFT JOIN
			`tabCube Table` ct ON ct.parent = c.name
		{where_clause}
		ORDER BY
			c.date_of_casting DESC, c.name, ct.idx
	"""

	data = frappe.db.sql(query, values, as_dict=1)

	return data
