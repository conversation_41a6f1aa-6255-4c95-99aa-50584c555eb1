// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["Cube Report"] = {
	"filters": [
		{
			"fieldname": "project",
			"label": __("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"width": 100
		},
		{
			"fieldname": "grade",
			"label": __("Grade"),
			"fieldtype": "Select",
			"options": "\nM10\nM15\nM20\nM25\nM30\nM35\nM40\nM45\nM50\nM55\nM60\nM65\nM70\nM75\nM80\nM85\nM90\nM95\nM100",
			"width": 100
		},
		{
			"fieldname": "age",
			"label": __("Age"),
			"fieldtype": "Data",
			"width": 100
		},
		{
			"fieldname": "from_date",
			"label": __("From Date"),
			"fieldtype": "Date",
			"default": frappe.datetime.add_months(frappe.datetime.get_today(), -1),
			"width": 100
		},
		{
			"fieldname": "to_date",
			"label": __("To Date"),
			"fieldtype": "Date",
			"default": frappe.datetime.get_today(),
			"width": 100
		}
	]
};
