// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["BJM & RP Report"] = {
	"filters": [
		{
			"fieldname": "material_type",
			"label": __("Material Type"),
			"fieldtype": "Select",
			"options": "Block Joint Mortar\nReady Plast",
			"reqd": 1,
			"default": "Block Joint Mortar"
		},
		{
			"fieldname": "project",
			"label": __("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"reqd": 0
		},
		{
			"fieldname": "supplier",
			"label": __("Supplier"),
			"fieldtype": "Link",
			"options": "Suppliers",
			"reqd": 0
		},
		{
			"fieldname": "brand",
			"label": __("Brand"),
			"fieldtype": "Data",
			"reqd": 0
		},
		{
			"fieldname": "from_date",
			"label": __("From Date"),
			"fieldtype": "Date",
			"reqd": 0
		},
		{
			"fieldname": "to_date",
			"label": __("To Date"),
			"fieldtype": "Date",
			"reqd": 0
		}
	]
};
