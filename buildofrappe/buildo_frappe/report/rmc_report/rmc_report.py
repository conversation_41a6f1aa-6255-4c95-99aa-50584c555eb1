# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _


def execute(filters=None):
	if not filters:
		filters = {}

	# Validate mandatory filters
	if not filters.get("grade"):
		frappe.throw(_("Grade is mandatory"))

	# Validate date range
	if filters.get("from_date") and filters.get("to_date"):
		from frappe.utils import getdate
		if getdate(filters.get("from_date")) > getdate(filters.get("to_date")):
			frappe.throw(_("From Date cannot be greater than To Date"))

	columns = get_columns()
	data = get_data(filters)

	return columns, data


def get_columns():
	"""Define the columns for the report"""
	return [
		{
			"fieldname": "project",
			"label": _("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"width": 150
		},
		{
			"fieldname": "grade",
			"label": _("Grade"),
			"fieldtype": "Data",
			"width": 80
		},
		{
			"fieldname": "date",
			"label": _("Date"),
			"fieldtype": "Date",
			"width": 100
		},
		{
			"fieldname": "supplier",
			"label": _("Supplier"),
			"fieldtype": "Link",
			"options": "Suppliers",
			"width": 150
		},
		{
			"fieldname": "challan_no",
			"label": _("Challan No"),
			"fieldtype": "Data",
			"width": 120
		},
		{
			"fieldname": "vehicle_no",
			"label": _("Vehicle No"),
			"fieldtype": "Data",
			"width": 120
		},
		{
			"fieldname": "time_in",
			"label": _("Time In"),
			"fieldtype": "Time",
			"width": 100
		},
		{
			"fieldname": "time_out",
			"label": _("Time Out"),
			"fieldtype": "Time",
			"width": 100
		},
		{
			"fieldname": "qty",
			"label": _("Quantity"),
			"fieldtype": "Float",
			"width": 100
		},
		{
			"fieldname": "remarks",
			"label": _("Remarks"),
			"fieldtype": "Data",
			"width": 200
		}
	]


def get_data(filters):
	"""Get the data for the report based on filters"""
	conditions = get_conditions(filters)

	query = """
		SELECT
			rmc.project,
			rmc.grade,
			rmc_table.date,
			rmc_table.supplier,
			rmc_table.challan_no,
			rmc_table.vehicle_no,
			rmc_table.time_in,
			rmc_table.time_out,
			rmc_table.qty,
			rmc_table.remarks
		FROM
			`tabRMC` rmc
		INNER JOIN
			`tabRmc table` rmc_table ON rmc_table.parent = rmc.name
		WHERE
			rmc.docstatus != 2
			{conditions}
		ORDER BY
			rmc_table.date DESC, rmc.name
	""".format(conditions=conditions)

	try:
		data = frappe.db.sql(query, filters, as_dict=1)
		return data
	except Exception as e:
		frappe.log_error(f"Error in RMC Report query: {str(e)}")
		frappe.throw(_("Error fetching data. Please check the filters and try again."))


def get_conditions(filters):
	"""Build WHERE conditions based on filters"""
	conditions = []

	# Grade filter (mandatory)
	if filters.get("grade"):
		conditions.append("AND rmc.grade = %(grade)s")

	# Project filter (optional)
	if filters.get("project"):
		conditions.append("AND rmc.project = %(project)s")

	# Date range filters (optional)
	if filters.get("from_date"):
		conditions.append("AND rmc_table.date >= %(from_date)s")

	if filters.get("to_date"):
		conditions.append("AND rmc_table.date <= %(to_date)s")

	return " ".join(conditions)
