// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["RMC Report"] = {
	"filters": [
		{
			"fieldname": "grade",
			"label": __("Grade"),
			"fieldtype": "Select",
			"options": "M10\nM15\nM20\nM25\nM30\nM35\nM40\nM45\nM50\nM55\nM60\nM65\nM70\nM75\nM80\nM85\nM90\nM95\nM100",
			"reqd": 1
		},
		{
			"fieldname": "project",
			"label": __("Project"),
			"fieldtype": "Link",
			"options": "Projects"
		},
		{
			"fieldname": "from_date",
			"label": __("From Date"),
			"fieldtype": "Date"
		},
		{
			"fieldname": "to_date",
			"label": __("To Date"),
			"fieldtype": "Date"
		}
	]
};
