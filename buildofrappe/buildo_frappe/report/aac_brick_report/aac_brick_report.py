# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _


def execute(filters=None):
	if not filters:
		filters = {}

	columns = get_columns(filters)
	data = get_data(filters)

	return columns, data


def get_columns(filters):
	"""Define the columns for the report"""
	item_type = filters.get("item_type", "AAC Block")
	doctype = "AAC Block" if item_type == "AAC Block" else "Bricks"

	columns = [
		{
			"fieldname": "name",
			"label": _("ID"),
			"fieldtype": "Link",
			"options": doctype,
			"width": 150
		},
		{
			"fieldname": "date",
			"label": _("Date"),
			"fieldtype": "Date",
			"width": 100
		},
		{
			"fieldname": "project",
			"label": _("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"width": 150
		},
		{
			"fieldname": "supplier",
			"label": _("Supplier"),
			"fieldtype": "Link",
			"options": "Suppliers",
			"width": 150
		},
		{
			"fieldname": "challan_no",
			"label": _("Challan No"),
			"fieldtype": "Data",
			"width": 120
		},
		{
			"fieldname": "vehicle_no",
			"label": _("Vehicle No"),
			"fieldtype": "Data",
			"width": 120
		},
		{
			"fieldname": "size",
			"label": _("Size"),
			"fieldtype": "Data",
			"width": 100
		},
		{
			"fieldname": "qty_nos",
			"label": _("Qty Nos"),
			"fieldtype": "Float",
			"width": 100
		},
		{
			"fieldname": "qty_m3",
			"label": _("Qty M3"),
			"fieldtype": "Float",
			"width": 100
		},
		{
			"fieldname": "remarks",
			"label": _("Remarks"),
			"fieldtype": "Data",
			"width": 200
		}
	]

	return columns


def get_data(filters):
	"""Get data based on filters"""
	item_type = filters.get("item_type")

	if not item_type:
		return []

	# Determine which doctype to query based on item_type
	if item_type == "AAC Block":
		doctype = "AAC Block"
	elif item_type == "Bricks":
		doctype = "Bricks"
	else:
		return []

	# Build conditions based on filters
	conditions = []
	values = {}

	if filters.get("project"):
		conditions.append("project = %(project)s")
		values["project"] = filters.get("project")

	if filters.get("supplier"):
		conditions.append("supplier = %(supplier)s")
		values["supplier"] = filters.get("supplier")

	if filters.get("size"):
		conditions.append("size = %(size)s")
		values["size"] = filters.get("size")

	if filters.get("from_date"):
		conditions.append("date >= %(from_date)s")
		values["from_date"] = filters.get("from_date")

	if filters.get("to_date"):
		conditions.append("date <= %(to_date)s")
		values["to_date"] = filters.get("to_date")

	# Build the WHERE clause
	where_clause = ""
	if conditions:
		where_clause = "WHERE " + " AND ".join(conditions)

	# Execute the query
	query = f"""
		SELECT
			name,
			date,
			project,
			supplier,
			challan_no,
			vehicle_no,
			size,
			qty_nos,
			qty_m3,
			remarks
		FROM `tab{doctype}`
		{where_clause}
		ORDER BY date DESC, name DESC
	"""

	data = frappe.db.sql(query, values, as_dict=True)

	return data
