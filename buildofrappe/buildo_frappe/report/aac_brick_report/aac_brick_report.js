// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["AAC Brick Report"] = {
	"filters": [
		{
			"fieldname": "item_type",
			"label": __("Item Type"),
			"fieldtype": "Select",
			"options": "\nAAC Block\nBricks",
			"reqd": 1,
			"default": "AAC Block"
		},
		{
			"fieldname": "project",
			"label": __("Project"),
			"fieldtype": "Link",
			"options": "Projects",
			"reqd": 0
		},
		{
			"fieldname": "supplier",
			"label": __("Supplier"),
			"fieldtype": "Link",
			"options": "Suppliers",
			"reqd": 0
		},
		{
			"fieldname": "size",
			"label": __("Size"),
			"fieldtype": "Select",
			"options": "\n4 inch\n6 inch\n9 inch",
			"reqd": 0
		},
		{
			"fieldname": "from_date",
			"label": __("From Date"),
			"fieldtype": "Date",
			"reqd": 0
		},
		{
			"fieldname": "to_date",
			"label": __("To Date"),
			"fieldtype": "Date",
			"reqd": 0
		}
	]
};
