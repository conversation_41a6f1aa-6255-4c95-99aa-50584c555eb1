# PO Doctype - Automatic Calculation Implementation

## Overview
Implemented automatic calculations for the PO (Purchase Order) doctype with the following features:

1. **Row-level calculation**: Amount = Rate × Qty (calculated automatically)
2. **Total Amount**: Sum of all row amounts
3. **Amount in Words**: Converts total amount to Indian currency format in words

## Changes Made

### 1. PO Table (po_table.json)
- Made `amount` field **read-only** since it's auto-calculated

### 2. PO Doctype (po.json)
- Made `total_amount` field **read-only**
- Made `amount_in_words` field **read-only**

### 3. Client-Side JavaScript (po.js)
Implemented real-time calculations that trigger on:
- When qty or rate changes in any row
- When a new row is added
- When a row is removed
- When the form loads

**Key Functions:**
- `calculate_row_amount()`: Calculates amount for a single row (rate × qty)
- `calculate_totals()`: Sums all row amounts and updates total_amount
- `in_words()`: Converts number to Indian currency format in words

### 4. Server-Side Validation (po.py)
Added validation to ensure calculations are correct even if data is manipulated:
- `validate()`: Called before saving the document
- `calculate_totals()`: Recalculates all amounts server-side
- `number_to_words()`: Converts amount to words in Indian format

### 5. Unit Tests (test_po.py)
Created comprehensive tests to verify:
- Row amount calculation (qty × rate)
- Total amount calculation (sum of all rows)
- Amount in words conversion
- Example: 22530560.70 → "Rupees Two Crore Twenty Five Lakh Thirty Thousand Five Hundred Sixty and Seventy Paise Only"

## How It Works

### Real-time Calculation Flow:
1. User enters/changes **qty** or **rate** in a table row
2. JavaScript automatically calculates **amount** = qty × rate
3. JavaScript recalculates **total_amount** = sum of all row amounts
4. JavaScript converts total to **amount_in_words** in Indian format

### Example:
```
Row 1: Item A, Qty: 10, Rate: 100 → Amount: 1,000
Row 2: Item B, Qty: 5, Rate: 200 → Amount: 1,000
Row 3: Item C, Qty: 2, Rate: 500 → Amount: 1,000

Total Amount: 3,000
Amount in Words: Rupees Three Thousand Only
```

### Indian Currency Format:
The amount in words follows Indian numbering system:
- Ones, Tens
- Hundreds
- Thousands
- Lakhs (100,000)
- Crores (10,000,000)

Example: 22,53,05,60.70
- 2 Crore
- 25 Lakh
- 30 Thousand
- 5 Hundred
- 60
- 70 Paise

Result: "Rupees Two Crore Twenty Five Lakh Thirty Thousand Five Hundred Sixty and Seventy Paise Only"

## Testing

To test the implementation:

1. **Manual Testing:**
   - Open a PO document
   - Add items to the table
   - Enter qty and rate values
   - Observe automatic calculation of amount, total, and words

2. **Unit Testing:**
   ```bash
   cd /home/<USER>/frappe-bench
   bench --site [your-site] run-tests --doctype "PO"
   ```

## Files Modified

1. `/buildofrappe/buildo_frappe/doctype/po/po.js` - Client-side calculations
2. `/buildofrappe/buildo_frappe/doctype/po/po.py` - Server-side validation
3. `/buildofrappe/buildo_frappe/doctype/po/po.json` - Made fields read-only
4. `/buildofrappe/buildo_frappe/doctype/po_table/po_table.json` - Made amount read-only
5. `/buildofrappe/buildo_frappe/doctype/po/test_po.py` - Unit tests

## Notes

- All calculations happen **automatically** as soon as values are changed
- Fields are **read-only** to prevent manual editing of calculated values
- Both **client-side** (JavaScript) and **server-side** (Python) validation ensures data integrity
- Supports **decimal values** with 2 decimal places for paise
- Follows **Indian currency format** for number-to-words conversion

