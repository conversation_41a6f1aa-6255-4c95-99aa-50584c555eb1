<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: #fff;
            padding: 2px;
        }

        /* Header Styles */
        .header {
            text-align: center;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 20px;
            text-decoration: underline;
            letter-spacing: 1px;
        }

        /* Information Section */
        .info-section {
            margin-bottom: 20px;
        }

        .info-row {
            display: flex;
            margin-bottom: 5px;
            align-items: center;
        }

        .info-label {
            font-weight: bold;
            width: 120px;
            flex-shrink: 0;
        }

        .date-info {
            margin-left: auto;
            font-weight: bold;
        }

        /* Section Titles */
        .section-title {
            font-weight: bold;
            text-align: center;
            margin: 20px 0 10px 0;
            text-decoration: underline;
            font-size: 14px;
        }

        .subsection-title {
            font-weight: bold;
            text-decoration: underline;
            text-align: center;
            margin-bottom: 10px;
            font-size: 12px;
        }

        /* Table Styles */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
        }

        th, td {
            border: 1px solid #000;
            padding: 4px 6px;
            text-align: center;
            vertical-align: middle;
        }

        th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 10px;
        }

        .text-left {
            text-align: left !important;
        }

        .total-row {
            font-weight: bold;
            background-color: #f8f8f8;
        }

        /* Work Status */
        .work-status-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .work-status-left,
        .work-status-right {
            flex: 1;
        }

        /* Hold Ups and Request Sections */
        .hold-ups-section,
        .request-info-section {
            margin: 20px 0;
        }

        .hold-ups-section h4,
        .request-info-section h4 {
            font-weight: bold;
            text-decoration: underline;
            text-align: center;
            margin-bottom: 10px;
            font-size: 12px;
        }

        .content-box {
            min-height: 30px;
            padding: 5px;
        }

        /* Equipment and Material Container */
        .equipment-material-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .equipment-section,
        .material-section {
            flex: 1;
        }

        /* Material Status Section */
        .material-status-section {
            margin-top: 20px;
        }

        .material-status-section h4 {
            font-weight: bold;
            text-decoration: underline;
            margin-bottom: 10px;
            font-size: 12px;
        }

        .material-status-content {
            padding: 10px;
        }

        .material-category strong {
            display: block;
            margin-bottom: 5px;
            text-decoration: underline;
        }

        .material-items {
            margin-left: 10px;
            line-height: 1.6;
        }

        /* Print Specific Styles */
        @media print {
            body {
                padding: 10px;
                font-size: 11px;
            }
            
            .header {
                font-size: 14px;
            }
            
            table {
                font-size: 10px;
            }
            
            th, td {
                padding: 3px 4px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        YOGI BUILDCON DAILY PROGRESS REPORT
    </div>
    
    <!-- Basic Information -->
    <div class="info-section">
        <div class="info-row">
            <span class="info-label">Client Name :</span>
            <span>{{ doc.client }}</span>
            <span class="date-info">Date : {{ doc.date.strftime('%d-%m-%Y') if doc.date else '' }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Project :</span>
            <span>{{ doc.project }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Address :</span>
            <span>{{ doc.address }}</span>
        </div>
    </div>
    
    <!-- Manpower Strength Report -->
    <div class="section-title">Manpower strength report</div>
    <table>
        <thead>
            <tr>
                <th>No</th>
                <th>Description</th>
                <th>Prev</th>
                <th>Today</th>
                <th>Specialized</th>
                <th>Carp</th>
                <th>Fitt</th>
                <th>B. Mas</th>
                <th>P.Mas</th>
                <th>M/C</th>
            </tr>
        </thead>
        <tbody>
            {% for row in doc.manpower_table %}
            <tr>
                <td>{{ loop.index }}</td>
                <td class="text-left">{{ row.description or '' }}</td>
                <td>{{ row.prev or 0 }}</td>
                <td>{{ row.today or 0 }}</td>
                <td>{{ row.specialized or 0 }}</td>
                <td>{{ row.carp or 0 }}</td>
                <td>{{ row.fitt or 0 }}</td>
                <td>{{ row.b_mas or 0 }}</td>
                <td>{{ row.pmas or 0 }}</td>
                <td>{{ row.mc or 0 }}</td>
            </tr>
            {% endfor %}
            <tr class="total-row">
                <td colspan="2">Total</td>
                <td>{{ doc.total_prev or 0 }}</td>
                <td>{{ doc.total_today or 0 }}</td>
                <td>{{ doc.total_spec or 0 }}</td>
                <td>{{ doc.total_carp or 0 }}</td>
                <td>{{ doc.total_fitt or 0 }}</td>
                <td>{{ doc.total_b_mas or 0 }}</td>
                <td>{{ doc.total_p_mas or 0 }}</td>
                <td>{{ doc.total_mc or 0 }}</td>
            </tr>
        </tbody>
    </table>
    
    <!-- Work Status -->
    <div class="section-title">Work Status</div>
    <div class="work-status-container">
        <div class="work-status-left">
            <table>
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Work Done Today</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in doc.wdone_today_table %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td class="text-left">{{ row.work_done_today or '' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="work-status-right">
            <table>
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Work Planned Tomorrow</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in doc.wplanned_tomm_table %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td class="text-left">{{ row.work_planned_tommorow or '' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Hold Ups / Stoppages -->
    <div class="hold-ups-section">
        <h4>Hold Ups /Stoppages of works with reasons</h4>
        <div class="content-box">{{ doc.hold_ups_stoppages_of_works_with_reasons or 'None' }}</div>
    </div>

    <!-- Request for Information -->
    <div class="request-info-section">
        <h4>Request for information</h4>
        <div class="content-box">{{ doc.request_for_information or 'None' }}</div>
    </div>

    <!-- Equipment and Material Section -->
    <div class="equipment-material-container">
        <div class="equipment-section">
            <h4 class="subsection-title">Equipment's/Machinery Deployed</h4>
            <table>
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Machinery Deployed</th>
                        <th>No(s)</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in doc.mach_deployed_table %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td class="text-left">{{ row.machinery_deployed or '' }}</td>
                        <td>{{ row.nos or '' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="material-section">
            <h4 class="subsection-title">Today's Material Receipt</h4>
            <table>
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Material</th>
                        <th>Qty</th>
                        <th>Desc</th>
                        <th>Remark</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in doc.mat_recp_table %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td class="text-left">{{ row.material or '' }}</td>
                        <td>{{ row.qty or '' }}</td>
                        <td class="text-left">{{ row.desc or '' }}</td>
                        <td class="text-left">{{ row.remark or '' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Status of Material -->
    <div class="material-status-section">
        <h4>Status of Material :</h4>
        <div class="material-status-content">
            <div class="material-category">
                <strong>Steel</strong>
                <div class="material-items">
                    {{ doc.status_of_material or '' }}
                </div>
            </div>
        </div>
    </div>

</body>
</html>
